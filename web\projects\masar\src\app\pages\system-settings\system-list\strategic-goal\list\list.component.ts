import { StrategicGoalService } from '../strategic-goal.service';
import { Component, OnD<PERSON>roy } from '@angular/core';
import { StrategicGoal } from '@masar/common/models';
import { finalize, tap } from 'rxjs/operators';
import { TableController } from '@masar/common/misc/table/table-controller';
import { NotificationService } from 'mnm-webapp';
import { TranslateService } from '@ngx-translate/core';

interface YearRangeStats {
    total: number;
    items: StrategicGoal[];
}

@Component({
    selector: 'app-list',
    templateUrl: './list.component.html',
})
export class ListComponent implements OnDestroy {
    public tableController: TableController<
        StrategicGoal,
        { keyword?: string }
    >;

    public orderOptions: number[];
    public currentlyDeleting: string[] = [];
    public yearRangeStats: { [yearRange: string]: YearRangeStats } = {};
    public validationMessages: { [yearRange: string]: string | null } = {};
    private allItems: StrategicGoal[] = []; // Store all items across all pages
    private currentKeyword: string = '';

    public constructor(
        private strategicGoalService: StrategicGoalService,
        private notificationService: NotificationService,
        private translateService: TranslateService
    ) {
        this.tableController = new TableController<
            StrategicGoal,
            { keyword?: string }
        >(
            filter =>
                strategicGoalService
                    .list(
                        filter.data.keyword,
                        filter.pageNumber,
                        filter.pageSize
                    )
                    .pipe(
                        tap(table => {
                            this.orderOptions = Array.from(
                                {
                                    length: table.count,
                                },
                                (_, k) => k + 1
                            );

                            // If keyword changed, reset all items
                            if (this.currentKeyword !== filter.data.keyword) {
                                this.allItems = [];
                                this.currentKeyword = filter.data.keyword || '';
                            }

                            // Add current page items to allItems if not already present
                            table.items.forEach(item => {
                                if (
                                    !this.allItems.find(
                                        existingItem =>
                                            existingItem.id === item.id
                                    )
                                ) {
                                    this.allItems.push(item);
                                }
                            });

                            // Calculate validation using all accumulated items
                            this.calculateYearRangeStats(this.allItems);
                            this.calculateValidationMessages();
                        })
                    ),
            { data: { keyword: '' } }
        );
        this.tableController.start();
    }

    public ngOnDestroy(): void {
        this.tableController.stop();
    }

    public delete(item: StrategicGoal): void {
        this.currentlyDeleting.push(item.id);
        this.strategicGoalService
            .delete(item.id)
            .pipe(
                finalize(() => {
                    this.currentlyDeleting = this.currentlyDeleting.filter(
                        x => x !== item.id
                    );
                })
            )
            .subscribe(message => {
                this.notificationService.notifySuccess(message);
                // Reset all items cache after deletion
                this.allItems = [];
                this.tableController.filter$.next(false);
            });
    }

    public onChangeOrder(item: StrategicGoal, order: number): void {
        this.strategicGoalService.updateOrder(item.id, order).subscribe(() => {
            // Reset all items cache after order change
            this.allItems = [];
            this.tableController.filter$.next(false);
        });
    }

    private calculateYearRangeStats(items: StrategicGoal[]): void {
        this.yearRangeStats = {};
        items.forEach(item => {
            if (!this.yearRangeStats[item.yearRange]) {
                this.yearRangeStats[item.yearRange] = {
                    total: 0,
                    items: [],
                };
            }
            this.yearRangeStats[item.yearRange].total += item.weight || 0;
            this.yearRangeStats[item.yearRange].items.push(item);
        });
    }

    private calculateValidationMessages(): void {
        this.validationMessages = {};
        Object.keys(this.yearRangeStats).forEach(yearRange => {
            const stats = this.yearRangeStats[yearRange];
            const totalPercent = Number((stats.total * 100).toFixed(2));

            if (totalPercent > 100) {
                this.validationMessages[yearRange] =
                    this.translateService.instant(
                        'translate_strategic_goals_weight_exceed_message',
                        {
                            currentTotal: totalPercent,
                        }
                    );
            } else if (totalPercent !== 100) {
                this.validationMessages[yearRange] =
                    this.translateService.instant(
                        'translate_strategic_goals_weight_not_100_message',
                        {
                            currentTotal: totalPercent,
                        }
                    );
            } else {
                this.validationMessages[yearRange] = null;
            }
        });
    }
}
